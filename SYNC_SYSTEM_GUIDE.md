# Complete Sync System Guide

Your LDIS application now has a **complete synchronization system** that automatically syncs data between SQLite (offline) and Supabase (online) databases!

## 🎯 What's New

### ✅ Complete Sync Implementation

1. **Template Sync**: Automatically syncs template changes (create, update, delete)
2. **Document Sync**: Syncs generated documents to the cloud
3. **User Sync**: Syncs user accounts and recovery options
4. **Conflict Resolution**: Handles conflicts with manual resolution options
5. **Change Tracking**: Tracks all offline changes for later sync
6. **Sync Dashboard**: Visual interface to monitor and manage sync

### 🔄 How It Works

1. **Offline Mode**: When offline, all changes are saved to SQLite and tracked for sync
2. **Online Detection**: System automatically detects when you come back online
3. **Auto Sync**: Pending changes are automatically synced when online
4. **Conflict Resolution**: If conflicts occur, they're flagged for manual resolution
5. **Real-time Status**: Dashboard shows sync progress and status

## 🚀 Key Features

### Automatic Change Tracking
- Every create, update, delete operation is tracked when offline
- Metadata stored in `sync_metadata` table
- Timestamps and operation details preserved

### Smart Conflict Resolution
- Detects conflicts between local and remote changes
- Provides UI for manual conflict resolution
- Options: Use Local, Use Remote, or Skip

### Comprehensive Dashboard
- Real-time sync status
- Progress tracking
- Conflict management
- Detailed statistics

### Robust Error Handling
- Graceful failure handling
- Retry mechanisms
- Detailed error reporting

## 📱 Usage

### Accessing the Sync Dashboard

Visit `/sync-status` in your application to see:
- Connection status (Online/Offline)
- Pending changes count
- Sync progress
- Conflict resolution interface

### Programmatic Usage

```typescript
import { syncAllData, getSyncStatus } from '@/lib/sync-service';
import { trackChange } from '@/lib/sync-metadata';

// Manual sync
const result = await syncAllData();

// Get sync status
const status = await getSyncStatus();

// Track a change (automatically done by models)
await trackChange('templates', 'template-id', 'create', templateData);
```

### React Hook

```typescript
import { useOfflineSync } from '@/hooks/useOfflineSync';

function MyComponent() {
  const {
    isOnline,
    isSupabaseMode,
    syncStatus,
    sync,
    isSyncing
  } = useOfflineSync();

  return (
    <div>
      <p>Status: {isOnline ? 'Online' : 'Offline'}</p>
      <p>Pending: {syncStatus?.pendingChanges || 0}</p>
      <button onClick={sync} disabled={isSyncing}>
        {isSyncing ? 'Syncing...' : 'Sync Now'}
      </button>
    </div>
  );
}
```

## 🛠️ Technical Details

### Database Schema

New `sync_metadata` table tracks changes:
```sql
CREATE TABLE sync_metadata (
  id BIGSERIAL PRIMARY KEY,
  table_name TEXT NOT NULL,
  record_id TEXT NOT NULL,
  operation TEXT NOT NULL, -- 'create', 'update', 'delete'
  sync_status TEXT DEFAULT 'pending', -- 'pending', 'synced', 'conflict'
  local_timestamp TIMESTAMPTZ NOT NULL,
  remote_timestamp TIMESTAMPTZ,
  conflict_data TEXT, -- JSON for conflict resolution
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Sync Process Flow

1. **Change Detection**: Models automatically track changes when offline
2. **Pending Queue**: Changes stored in `sync_metadata` table
3. **Sync Trigger**: Manual sync or auto-sync when online
4. **Conflict Check**: Compare local vs remote timestamps
5. **Resolution**: Apply changes or flag conflicts
6. **Cleanup**: Mark as synced or conflict

### File Structure

```
src/lib/
├── sync-metadata.ts          # Core sync metadata management
├── sync-service.ts           # Main sync orchestration
├── database-hybrid.ts        # Database switching logic
├── models/
│   ├── template-hybrid.ts    # Template sync logic
│   ├── user-hybrid.ts        # User sync logic
│   └── *-sqlite-backup.ts    # Change tracking in models
src/components/
├── SyncDashboard.tsx         # Main sync dashboard
├── SyncConflictResolver.tsx  # Conflict resolution UI
└── DatabaseStatusIndicator.tsx # Status indicator
src/hooks/
└── useOfflineSync.ts         # React hook for sync
```

## 🧪 Testing

### Run Integration Tests

```bash
# Run the integration test script
npx tsx src/scripts/test-sync.ts

# Run unit tests
npm test src/lib/__tests__/sync-system.test.ts
```

### Manual Testing Scenarios

1. **Offline Creation**:
   - Go offline (disable network)
   - Create templates/users
   - Go online and check sync

2. **Conflict Resolution**:
   - Modify same record offline and online
   - Sync and resolve conflicts

3. **Auto Sync**:
   - Make offline changes
   - Come back online
   - Watch auto-sync trigger

## 🔧 Configuration

### Environment Variables

```env
# Required for online sync
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### Sync Settings

```typescript
// Auto-sync interval (default: 5 minutes)
const AUTO_SYNC_INTERVAL = 5 * 60 * 1000;

// Connectivity check interval (default: 30 seconds)
const CONNECTIVITY_CHECK_INTERVAL = 30000;
```

## 🚨 Important Notes

### Data Consistency
- **Local First**: SQLite is the source of truth when offline
- **Conflict Resolution**: Manual resolution required for conflicts
- **Atomic Operations**: Each sync operation is atomic

### Performance
- **Batch Sync**: Multiple changes synced together
- **Incremental**: Only pending changes are synced
- **Background**: Sync runs in background without blocking UI

### Security
- **Encrypted Storage**: SQLite data is encrypted
- **Secure Transport**: All sync operations use HTTPS
- **Authentication**: Supabase handles authentication and authorization

## 🔍 Troubleshooting

### Common Issues

1. **Sync Not Working**
   - Check network connectivity
   - Verify Supabase configuration
   - Check browser console for errors

2. **Conflicts Not Resolving**
   - Use the conflict resolution UI
   - Check conflict data in sync dashboard
   - Manual resolution may be required

3. **Performance Issues**
   - Check pending changes count
   - Run cleanup for old sync records
   - Monitor network requests

### Debug Mode

Enable debug logging:
```typescript
// In browser console
localStorage.setItem('debug', 'sync:*');
```

## 🎉 Success!

Your sync system is now complete and ready for production use! The system provides:

- ✅ Automatic offline/online detection
- ✅ Complete data synchronization
- ✅ Conflict resolution
- ✅ Real-time status monitoring
- ✅ Comprehensive error handling
- ✅ User-friendly dashboard

Visit `/sync-status` to see it in action!
