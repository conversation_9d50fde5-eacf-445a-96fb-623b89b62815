// Database initialization script
// Run this to create the SQLite database: npx tsx src/scripts/init-database.js

const Database = require("better-sqlite3");
const fs = require("fs");
const path = require("path");

async function initializeDatabase() {
  try {
    console.log("🚀 Initializing LDIS SQLite database...");

    // Ensure data directory exists
    const dataDir = path.join(process.cwd(), "data");
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
      console.log("📁 Created data directory");
    }

    // Create database file
    const dbPath = path.join(dataDir, "ldis.db");
    const db = new Database(dbPath);

    // Enable WAL mode for better performance
    db.pragma("journal_mode = WAL");

    console.log("📁 Database file created at:", dbPath);

    // Create tables
    console.log("📋 Creating tables...");

    // Users table
    db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        recovery_options TEXT, -- JSON string
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log("   ✅ users table created");

    // Templates table
    db.exec(`
      CREATE TABLE IF NOT EXISTS templates (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        filename TEXT NOT NULL,
        placeholders TEXT, -- JSON array
        layout_size TEXT DEFAULT 'A4',
        has_applicant_photo BOOLEAN DEFAULT FALSE,
        uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log("   ✅ templates table created");

    // Documents table (for generated documents)
    db.exec(`
      CREATE TABLE IF NOT EXISTS documents (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        template_id TEXT NOT NULL,
        document_data TEXT, -- JSON string of form data
        generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (template_id) REFERENCES templates (id)
      )
    `);
    console.log("   ✅ documents table created");

    // Sync metadata table (for tracking sync status)
    db.exec(`
      CREATE TABLE IF NOT EXISTS sync_metadata (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        table_name TEXT NOT NULL,
        record_id TEXT NOT NULL,
        operation TEXT NOT NULL, -- 'create', 'update', 'delete'
        sync_status TEXT DEFAULT 'pending', -- 'pending', 'synced', 'conflict'
        local_timestamp DATETIME NOT NULL,
        remote_timestamp DATETIME,
        conflict_data TEXT, -- JSON string for conflict resolution
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(table_name, record_id, operation)
      )
    `);
    console.log("   ✅ sync_metadata table created");

    // Create indexes for better performance
    db.exec(`
      CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
      CREATE INDEX IF NOT EXISTS idx_templates_name ON templates(name);
      CREATE INDEX IF NOT EXISTS idx_documents_template_id ON documents(template_id);
      CREATE INDEX IF NOT EXISTS idx_documents_generated_at ON documents(generated_at);
      CREATE INDEX IF NOT EXISTS idx_sync_metadata_table_record ON sync_metadata(table_name, record_id);
      CREATE INDEX IF NOT EXISTS idx_sync_metadata_status ON sync_metadata(sync_status);
      CREATE INDEX IF NOT EXISTS idx_sync_metadata_timestamp ON sync_metadata(local_timestamp);
    `);
    console.log("   ✅ indexes created");

    // Close database
    db.close();

    console.log("✅ Database initialized successfully!");
    console.log("🎉 Your LDIS database is ready to use!");
  } catch (error) {
    console.error("❌ Error initializing database:", error);
    process.exit(1);
  }
}

// Run the initialization
initializeDatabase();
