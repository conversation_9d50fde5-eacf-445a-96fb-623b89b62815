// Hybrid user model - automatically switches between Supabase and SQLite based on connectivity

import { getDatabaseStatus } from '../database-hybrid';

// Dynamic imports for model implementations
let supabaseUserModel: any;
let sqliteUserModel: any;

// Initialize model modules
const initModelModules = () => {
  if (!supabaseUserModel) {
    supabaseUserModel = require('./user-supabase');
  }
  if (!sqliteUserModel) {
    sqliteUserModel = require('./user-sqlite-backup');
  }
};

// Get the appropriate model implementation
const getUserModelImpl = async () => {
  initModelModules();
  
  const status = await getDatabaseStatus();
  return status.usingSupabase ? supabaseUserModel : sqliteUserModel;
};

// Re-export types (same for both implementations)
export type User = {
  id?: number;
  username: string;
  passwordHash: string;
  recoveryOptions: {
    privateKey?: string;
    securityQuestions?: {
      question: string;
      answerHash: string;
    }[];
  };
  createdAt?: string;
  updatedAt?: string;
};

export type LoginCredentials = {
  username: string;
  password: string;
};

export type SignupData = {
  username: string;
  password: string;
  recoveryMethod: 'privateKey' | 'securityQuestions';
  privateKey?: string;
  securityQuestions?: {
    question: string;
    answer: string;
  }[];
};

export type RecoveryData = {
  username: string;
  recoveryMethod: 'privateKey' | 'securityQuestions';
  privateKey?: string;
  securityAnswers?: string[];
};

// Hybrid UserModel class
export class UserModel {
  static async findByUsername(username: string): Promise<User | null> {
    const impl = await getUserModelImpl();
    return impl.UserModel.findByUsername(username);
  }

  static async create(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    const impl = await getUserModelImpl();
    return impl.UserModel.create(userData);
  }

  static async updatePassword(username: string, newPasswordHash: string): Promise<boolean> {
    const impl = await getUserModelImpl();
    return impl.UserModel.updatePassword(username, newPasswordHash);
  }

  static async getAllUsers(): Promise<User[]> {
    const impl = await getUserModelImpl();
    return impl.UserModel.getAllUsers();
  }

  static async deleteUser(username: string): Promise<boolean> {
    const impl = await getUserModelImpl();
    return impl.UserModel.deleteUser(username);
  }
}

// Hybrid authentication functions
export const signup = async (signupData: SignupData): Promise<{ success: boolean; message: string; user?: User }> => {
  const impl = await getUserModelImpl();
  return impl.signup(signupData);
};

export const login = async (credentials: LoginCredentials): Promise<{ success: boolean; message: string; user?: User }> => {
  const impl = await getUserModelImpl();
  return impl.login(credentials);
};

export const recoverAccount = async (recoveryData: RecoveryData): Promise<{ success: boolean; message: string; newPassword?: string }> => {
  const impl = await getUserModelImpl();
  return impl.recoverAccount(recoveryData);
};

// Utility functions
export const hashPassword = async (password: string): Promise<string> => {
  const impl = await getUserModelImpl();
  return impl.hashPassword(password);
};

export const generatePrivateKey = async (): Promise<string> => {
  const impl = await getUserModelImpl();
  return impl.generatePrivateKey();
};

export const encrypt = async (text: string): Promise<string> => {
  const impl = await getUserModelImpl();
  return impl.encrypt(text);
};

export const decrypt = async (ciphertext: string): Promise<string> => {
  const impl = await getUserModelImpl();
  return impl.decrypt(ciphertext);
};

// Constants
export const SECURITY_QUESTIONS = [
  "What was the name of your childhood best friend?",
  "What is the name of the street you grew up on?",
  "What was the name of your first pet?",
  "What was your favorite subject in school?",
  "What is the middle name of your oldest sibling?"
];

// Sync function for offline changes
export const syncUserChanges = async (): Promise<{
  success: boolean;
  synced: number;
  errors: any[];
}> => {
  console.log('🔄 Starting user sync...');

  try {
    const { getPendingChanges, markAsSynced, markAsConflict } = await import('../sync-metadata');

    // Get pending user changes
    const pendingChanges = await getPendingChanges('users');

    if (pendingChanges.length === 0) {
      console.log('✅ No pending user changes to sync');
      return { success: true, synced: 0, errors: [] };
    }

    console.log(`📋 Found ${pendingChanges.length} pending user changes`);

    // Get both model implementations
    initModelModules();
    const supabaseModel = supabaseUserModel.UserModel;
    const sqliteModel = sqliteUserModel.UserModel;

    let syncedCount = 0;
    const errors: any[] = [];

    for (const change of pendingChanges) {
      try {
        console.log(`🔄 Syncing ${change.operation} for user ${change.recordId}`);

        if (change.operation === 'create') {
          // Create user in Supabase
          const userData = change.conflictData;
          await supabaseModel.create(userData);
          await markAsSynced('users', change.recordId, 'create');
          syncedCount++;

        } else if (change.operation === 'update') {
          // Check if user exists in Supabase
          const existingUser = await supabaseModel.findById(parseInt(change.recordId));

          if (existingUser) {
            // Update user in Supabase
            const updates = change.conflictData;
            if (updates.recoveryOptions) {
              await supabaseModel.updateRecoveryOptions(existingUser.username, updates.recoveryOptions);
            }
            await markAsSynced('users', change.recordId, 'update');
            syncedCount++;
          } else {
            // User doesn't exist in Supabase, get full data from SQLite and create
            const fullUser = await sqliteModel.findById(parseInt(change.recordId));
            if (fullUser) {
              await supabaseModel.create(fullUser);
              await markAsSynced('users', change.recordId, 'update');
              syncedCount++;
            } else {
              throw new Error(`User ${change.recordId} not found in local database`);
            }
          }
        }
        // Note: User deletion is typically not implemented for security reasons

      } catch (error) {
        console.error(`❌ Failed to sync ${change.operation} for user ${change.recordId}:`, error);

        // Mark as conflict for manual resolution
        await markAsConflict('users', change.recordId, change.operation, {
          error: error instanceof Error ? error.message : 'Unknown error',
          originalData: change.conflictData,
          timestamp: new Date().toISOString()
        });

        errors.push({
          recordId: change.recordId,
          operation: change.operation,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    console.log(`✅ User sync completed: ${syncedCount} synced, ${errors.length} errors`);

    return {
      success: errors.length === 0,
      synced: syncedCount,
      errors
    };

  } catch (error) {
    console.error('❌ User sync failed:', error);
    return {
      success: false,
      synced: 0,
      errors: [error instanceof Error ? error.message : 'Unknown error']
    };
  }
};
