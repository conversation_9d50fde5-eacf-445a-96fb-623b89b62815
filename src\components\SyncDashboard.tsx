'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  RefreshCw, 
  Database, 
  Cloud, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Users,
  FileText,
  File
} from 'lucide-react';
import { useOfflineSync } from '@/hooks/useOfflineSync';
import SyncConflictResolver from './SyncConflictResolver';

interface SyncStats {
  pending: number;
  synced: number;
  conflicts: number;
  total: number;
}

export default function SyncDashboard() {
  const {
    isOnline,
    isSupabaseMode,
    isSQLiteMode,
    syncStatus,
    sync,
    isSyncing,
    lastSyncResult,
    error
  } = useOfflineSync();

  const [syncStats, setSyncStats] = useState<SyncStats>({ pending: 0, synced: 0, conflicts: 0, total: 0 });
  const [loading, setLoading] = useState(true);

  const loadSyncStats = async () => {
    try {
      setLoading(true);
      const { getSyncStats } = await import('@/lib/sync-metadata');
      const stats = await getSyncStats();
      setSyncStats(stats);
    } catch (error) {
      console.error('Error loading sync stats:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSyncStats();
  }, []);

  const handleSync = async () => {
    await sync();
    await loadSyncStats();
  };

  const handleConflictResolved = () => {
    loadSyncStats();
  };

  const getSyncProgress = () => {
    if (syncStats.total === 0) return 100;
    return Math.round((syncStats.synced / syncStats.total) * 100);
  };

  return (
    <div className="space-y-6">
      {/* Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Connection Status</CardTitle>
            {isOnline ? (
              <Cloud className="h-4 w-4 text-green-500" />
            ) : (
              <Database className="h-4 w-4 text-gray-500" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isSupabaseMode ? 'Online' : 'Offline'}
            </div>
            <p className="text-xs text-muted-foreground">
              {isSupabaseMode ? 'Using Supabase' : 'Using SQLite'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Changes</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{syncStats.pending}</div>
            <p className="text-xs text-muted-foreground">
              {syncStats.pending === 0 ? 'All synced' : 'Need sync'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conflicts</CardTitle>
            {syncStats.conflicts > 0 ? (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            ) : (
              <CheckCircle className="h-4 w-4 text-green-500" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{syncStats.conflicts}</div>
            <p className="text-xs text-muted-foreground">
              {syncStats.conflicts === 0 ? 'No conflicts' : 'Need resolution'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Sync Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Sync Progress</span>
            <Button 
              onClick={handleSync} 
              disabled={isSyncing || !isOnline}
              size="sm"
            >
              {isSyncing ? (
                <RefreshCw className="w-4 h-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="w-4 h-4 mr-2" />
              )}
              {isSyncing ? 'Syncing...' : 'Sync Now'}
            </Button>
          </CardTitle>
          <CardDescription>
            {syncStats.total > 0 ? (
              <>
                {syncStats.synced} of {syncStats.total} items synchronized
              </>
            ) : (
              'All data is synchronized'
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Progress value={getSyncProgress()} className="w-full" />
          <div className="flex justify-between text-sm text-muted-foreground mt-2">
            <span>{getSyncProgress()}% complete</span>
            {syncStatus?.lastSync && (
              <span>Last sync: {new Date(syncStatus.lastSync).toLocaleString()}</span>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200">
          <CardHeader>
            <CardTitle className="text-red-700 flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2" />
              Sync Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Last Sync Result */}
      {lastSyncResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              {lastSyncResult.success ? (
                <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
              ) : (
                <AlertTriangle className="w-5 h-5 text-red-500 mr-2" />
              )}
              Last Sync Result
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4" />
                <span className="text-sm">Users: {lastSyncResult.details.users.synced}</span>
                {lastSyncResult.details.users.errors.length > 0 && (
                  <Badge variant="destructive" className="text-xs">
                    {lastSyncResult.details.users.errors.length} errors
                  </Badge>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <FileText className="w-4 h-4" />
                <span className="text-sm">Templates: {lastSyncResult.details.templates.synced}</span>
                {lastSyncResult.details.templates.errors.length > 0 && (
                  <Badge variant="destructive" className="text-xs">
                    {lastSyncResult.details.templates.errors.length} errors
                  </Badge>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <File className="w-4 h-4" />
                <span className="text-sm">Documents: {lastSyncResult.details.documents.synced}</span>
                {lastSyncResult.details.documents.errors.length > 0 && (
                  <Badge variant="destructive" className="text-xs">
                    {lastSyncResult.details.documents.errors.length} errors
                  </Badge>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Detailed Tabs */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="conflicts">
            Conflicts
            {syncStats.conflicts > 0 && (
              <Badge variant="destructive" className="ml-2 text-xs">
                {syncStats.conflicts}
              </Badge>
            )}
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Sync Statistics</CardTitle>
              <CardDescription>
                Overview of synchronization status across all data types
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>Total Records</span>
                  <Badge variant="outline">{syncStats.total}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Synchronized</span>
                  <Badge variant="default">{syncStats.synced}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Pending Sync</span>
                  <Badge variant="secondary">{syncStats.pending}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Conflicts</span>
                  <Badge variant={syncStats.conflicts > 0 ? "destructive" : "default"}>
                    {syncStats.conflicts}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="conflicts">
          <SyncConflictResolver onConflictResolved={handleConflictResolved} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
