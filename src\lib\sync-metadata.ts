// Sync metadata service for tracking changes and sync status
// Works with both SQLite and Supabase

import { getDatabaseStatus } from './database-hybrid';

export interface SyncMetadata {
  id?: number;
  tableName: string;
  recordId: string;
  operation: 'create' | 'update' | 'delete';
  syncStatus: 'pending' | 'synced' | 'conflict';
  localTimestamp: string;
  remoteTimestamp?: string;
  conflictData?: any;
  createdAt?: string;
  updatedAt?: string;
}

// Dynamic imports for database implementations
let supabaseDb: any;
let sqliteDb: any;

// Initialize database modules
const initDatabaseModules = () => {
  if (!supabaseDb) {
    supabaseDb = require('./database-supabase');
  }
  if (!sqliteDb) {
    sqliteDb = require('./database-sqlite-backup');
  }
};

// Get the appropriate database implementation
const getDatabaseImpl = async () => {
  initDatabaseModules();
  
  const status = await getDatabaseStatus();
  return status.usingSupabase ? supabaseDb : sqliteDb;
};

// Track a change for sync
export const trackChange = async (
  tableName: string,
  recordId: string,
  operation: 'create' | 'update' | 'delete',
  conflictData?: any
): Promise<void> => {
  try {
    const db = await getDatabaseImpl();
    const timestamp = new Date().toISOString();

    if (db === supabaseDb) {
      // Supabase implementation
      const { supabaseAdmin } = require('./supabase');
      
      await supabaseAdmin
        .from('sync_metadata')
        .upsert({
          table_name: tableName,
          record_id: recordId,
          operation,
          sync_status: 'pending',
          local_timestamp: timestamp,
          conflict_data: conflictData ? JSON.stringify(conflictData) : null
        }, {
          onConflict: 'table_name,record_id,operation'
        });
    } else {
      // SQLite implementation
      const database = await db.getDatabase();
      
      const stmt = database.prepare(`
        INSERT OR REPLACE INTO sync_metadata 
        (table_name, record_id, operation, sync_status, local_timestamp, conflict_data)
        VALUES (?, ?, ?, 'pending', ?, ?)
      `);
      
      stmt.run(
        tableName,
        recordId,
        operation,
        timestamp,
        conflictData ? JSON.stringify(conflictData) : null
      );
    }
    
    console.log(`📝 Tracked ${operation} for ${tableName}:${recordId}`);
  } catch (error) {
    console.error('Error tracking change:', error);
    throw error;
  }
};

// Get pending changes for sync
export const getPendingChanges = async (tableName?: string): Promise<SyncMetadata[]> => {
  try {
    const db = await getDatabaseImpl();

    if (db === supabaseDb) {
      // Supabase implementation
      const { supabaseAdmin } = require('./supabase');
      
      let query = supabaseAdmin
        .from('sync_metadata')
        .select('*')
        .eq('sync_status', 'pending')
        .order('local_timestamp', { ascending: true });

      if (tableName) {
        query = query.eq('table_name', tableName);
      }

      const { data, error } = await query;
      
      if (error) throw error;

      return (data || []).map(row => ({
        id: row.id,
        tableName: row.table_name,
        recordId: row.record_id,
        operation: row.operation,
        syncStatus: row.sync_status,
        localTimestamp: row.local_timestamp,
        remoteTimestamp: row.remote_timestamp,
        conflictData: row.conflict_data ? JSON.parse(row.conflict_data) : undefined,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }));
    } else {
      // SQLite implementation
      const database = await db.getDatabase();
      
      let sql = `
        SELECT * FROM sync_metadata 
        WHERE sync_status = 'pending'
      `;
      
      if (tableName) {
        sql += ` AND table_name = ?`;
      }
      
      sql += ` ORDER BY local_timestamp ASC`;
      
      const stmt = database.prepare(sql);
      const rows = tableName ? stmt.all(tableName) : stmt.all();
      
      return rows.map((row: any) => ({
        id: row.id,
        tableName: row.table_name,
        recordId: row.record_id,
        operation: row.operation,
        syncStatus: row.sync_status,
        localTimestamp: row.local_timestamp,
        remoteTimestamp: row.remote_timestamp,
        conflictData: row.conflict_data ? JSON.parse(row.conflict_data) : undefined,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }));
    }
  } catch (error) {
    console.error('Error getting pending changes:', error);
    throw error;
  }
};

// Mark change as synced
export const markAsSynced = async (
  tableName: string,
  recordId: string,
  operation: string,
  remoteTimestamp?: string
): Promise<void> => {
  try {
    const db = await getDatabaseImpl();

    if (db === supabaseDb) {
      // Supabase implementation
      const { supabaseAdmin } = require('./supabase');
      
      await supabaseAdmin
        .from('sync_metadata')
        .update({
          sync_status: 'synced',
          remote_timestamp: remoteTimestamp || new Date().toISOString()
        })
        .eq('table_name', tableName)
        .eq('record_id', recordId)
        .eq('operation', operation);
    } else {
      // SQLite implementation
      const database = await db.getDatabase();
      
      const stmt = database.prepare(`
        UPDATE sync_metadata 
        SET sync_status = 'synced', remote_timestamp = ?
        WHERE table_name = ? AND record_id = ? AND operation = ?
      `);
      
      stmt.run(
        remoteTimestamp || new Date().toISOString(),
        tableName,
        recordId,
        operation
      );
    }
    
    console.log(`✅ Marked ${operation} as synced for ${tableName}:${recordId}`);
  } catch (error) {
    console.error('Error marking as synced:', error);
    throw error;
  }
};

// Mark change as conflict
export const markAsConflict = async (
  tableName: string,
  recordId: string,
  operation: string,
  conflictData: any
): Promise<void> => {
  try {
    const db = await getDatabaseImpl();

    if (db === supabaseDb) {
      // Supabase implementation
      const { supabaseAdmin } = require('./supabase');
      
      await supabaseAdmin
        .from('sync_metadata')
        .update({
          sync_status: 'conflict',
          conflict_data: JSON.stringify(conflictData)
        })
        .eq('table_name', tableName)
        .eq('record_id', recordId)
        .eq('operation', operation);
    } else {
      // SQLite implementation
      const database = await db.getDatabase();
      
      const stmt = database.prepare(`
        UPDATE sync_metadata 
        SET sync_status = 'conflict', conflict_data = ?
        WHERE table_name = ? AND record_id = ? AND operation = ?
      `);
      
      stmt.run(
        JSON.stringify(conflictData),
        tableName,
        recordId,
        operation
      );
    }
    
    console.log(`⚠️ Marked ${operation} as conflict for ${tableName}:${recordId}`);
  } catch (error) {
    console.error('Error marking as conflict:', error);
    throw error;
  }
};

// Get sync statistics
export const getSyncStats = async (): Promise<{
  pending: number;
  synced: number;
  conflicts: number;
  total: number;
}> => {
  try {
    const db = await getDatabaseImpl();

    if (db === supabaseDb) {
      // Supabase implementation
      const { supabaseAdmin } = require('./supabase');
      
      const { data, error } = await supabaseAdmin
        .from('sync_metadata')
        .select('sync_status');
      
      if (error) throw error;

      const stats = { pending: 0, synced: 0, conflicts: 0, total: 0 };
      
      (data || []).forEach((row: any) => {
        stats.total++;
        if (row.sync_status === 'pending') stats.pending++;
        else if (row.sync_status === 'synced') stats.synced++;
        else if (row.sync_status === 'conflict') stats.conflicts++;
      });

      return stats;
    } else {
      // SQLite implementation
      const database = await db.getDatabase();
      
      const stmt = database.prepare(`
        SELECT sync_status, COUNT(*) as count 
        FROM sync_metadata 
        GROUP BY sync_status
      `);
      
      const rows = stmt.all();
      const stats = { pending: 0, synced: 0, conflicts: 0, total: 0 };
      
      rows.forEach((row: any) => {
        stats.total += row.count;
        if (row.sync_status === 'pending') stats.pending = row.count;
        else if (row.sync_status === 'synced') stats.synced = row.count;
        else if (row.sync_status === 'conflict') stats.conflicts = row.count;
      });

      return stats;
    }
  } catch (error) {
    console.error('Error getting sync stats:', error);
    throw error;
  }
};

// Clean up old synced records (optional maintenance)
export const cleanupSyncedRecords = async (olderThanDays: number = 30): Promise<number> => {
  try {
    const db = await getDatabaseImpl();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
    const cutoffTimestamp = cutoffDate.toISOString();

    if (db === supabaseDb) {
      // Supabase implementation
      const { supabaseAdmin } = require('./supabase');
      
      const { data, error } = await supabaseAdmin
        .from('sync_metadata')
        .delete()
        .eq('sync_status', 'synced')
        .lt('remote_timestamp', cutoffTimestamp)
        .select('id');
      
      if (error) throw error;
      
      return (data || []).length;
    } else {
      // SQLite implementation
      const database = await db.getDatabase();
      
      const stmt = database.prepare(`
        DELETE FROM sync_metadata 
        WHERE sync_status = 'synced' AND remote_timestamp < ?
      `);
      
      const result = stmt.run(cutoffTimestamp);
      return result.changes;
    }
  } catch (error) {
    console.error('Error cleaning up synced records:', error);
    throw error;
  }
};
