import { Metadata } from 'next';
import SyncDashboard from '@/components/SyncDashboard';
import DatabaseStatusIndicator from '@/components/DatabaseStatusIndicator';

export const metadata: Metadata = {
  title: 'Sync Status - LDIS',
  description: 'Monitor and manage data synchronization between local and cloud databases',
};

export default function SyncStatusPage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Sync Status</h1>
          <p className="text-muted-foreground">
            Monitor and manage data synchronization between local and cloud databases
          </p>
        </div>
        <DatabaseStatusIndicator showDetails showSyncButton />
      </div>

      <SyncDashboard />
    </div>
  );
}
