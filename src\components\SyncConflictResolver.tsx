'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, CheckCircle, XCircle, RefreshCw } from 'lucide-react';
import { SyncMetadata } from '@/lib/sync-metadata';

interface SyncConflictResolverProps {
  onConflictResolved?: () => void;
}

export default function SyncConflictResolver({ onConflictResolved }: SyncConflictResolverProps) {
  const [conflicts, setConflicts] = useState<SyncMetadata[]>([]);
  const [loading, setLoading] = useState(true);
  const [resolving, setResolving] = useState<string | null>(null);

  const loadConflicts = async () => {
    try {
      setLoading(true);
      const { getPendingChanges } = await import('@/lib/sync-metadata');
      
      // Get all pending changes and filter for conflicts
      const allPending = await getPendingChanges();
      const conflictChanges = allPending.filter(change => change.syncStatus === 'conflict');
      
      setConflicts(conflictChanges);
    } catch (error) {
      console.error('Error loading conflicts:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConflicts();
  }, []);

  const resolveConflict = async (conflict: SyncMetadata, resolution: 'use_local' | 'use_remote' | 'skip') => {
    try {
      setResolving(conflict.recordId);
      
      const { markAsSynced, markAsConflict } = await import('@/lib/sync-metadata');
      
      if (resolution === 'use_local') {
        // Force sync the local version
        if (conflict.tableName === 'templates') {
          const { syncTemplateChanges } = await import('@/lib/models/template-hybrid');
          await syncTemplateChanges();
        } else if (conflict.tableName === 'documents') {
          const { syncDocumentChanges } = await import('@/lib/models/template-hybrid');
          await syncDocumentChanges();
        }
        
        await markAsSynced(conflict.tableName, conflict.recordId, conflict.operation);
      } else if (resolution === 'use_remote') {
        // Mark as synced (accept remote version)
        await markAsSynced(conflict.tableName, conflict.recordId, conflict.operation);
      } else if (resolution === 'skip') {
        // Keep as conflict for later resolution
        await markAsConflict(conflict.tableName, conflict.recordId, conflict.operation, {
          ...conflict.conflictData,
          skipped: true,
          skippedAt: new Date().toISOString()
        });
      }
      
      // Reload conflicts
      await loadConflicts();
      
      if (onConflictResolved) {
        onConflictResolved();
      }
      
    } catch (error) {
      console.error('Error resolving conflict:', error);
    } finally {
      setResolving(null);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <RefreshCw className="w-5 h-5 animate-spin" />
            <span>Loading Conflicts...</span>
          </CardTitle>
        </CardHeader>
      </Card>
    );
  }

  if (conflicts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CheckCircle className="w-5 h-5 text-green-500" />
            <span>No Sync Conflicts</span>
          </CardTitle>
          <CardDescription>
            All changes have been synchronized successfully.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-yellow-500" />
            <span>Sync Conflicts ({conflicts.length})</span>
          </CardTitle>
          <CardDescription>
            These changes couldn't be synchronized automatically and need manual resolution.
          </CardDescription>
        </CardHeader>
      </Card>

      {conflicts.map((conflict) => (
        <Card key={`${conflict.tableName}-${conflict.recordId}-${conflict.operation}`}>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="capitalize">{conflict.operation}</span>
                <span>{conflict.tableName}</span>
                <Badge variant="outline">{conflict.recordId}</Badge>
              </div>
              <Badge variant="destructive">Conflict</Badge>
            </CardTitle>
            <CardDescription>
              Local change: {new Date(conflict.localTimestamp).toLocaleString()}
              {conflict.remoteTimestamp && (
                <><br />Remote change: {new Date(conflict.remoteTimestamp).toLocaleString()}</>
              )}
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            {conflict.conflictData?.error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-700">
                  <strong>Error:</strong> {conflict.conflictData.error}
                </p>
              </div>
            )}
            
            {conflict.conflictData?.originalData && (
              <div className="mb-4 p-3 bg-gray-50 border border-gray-200 rounded-md">
                <p className="text-sm font-medium mb-2">Local Data:</p>
                <pre className="text-xs overflow-x-auto">
                  {JSON.stringify(conflict.conflictData.originalData, null, 2)}
                </pre>
              </div>
            )}
            
            <div className="flex space-x-2">
              <Button
                size="sm"
                variant="default"
                onClick={() => resolveConflict(conflict, 'use_local')}
                disabled={resolving === conflict.recordId}
              >
                {resolving === conflict.recordId ? (
                  <RefreshCw className="w-4 h-4 animate-spin mr-2" />
                ) : (
                  <CheckCircle className="w-4 h-4 mr-2" />
                )}
                Use Local
              </Button>
              
              <Button
                size="sm"
                variant="outline"
                onClick={() => resolveConflict(conflict, 'use_remote')}
                disabled={resolving === conflict.recordId}
              >
                <XCircle className="w-4 h-4 mr-2" />
                Use Remote
              </Button>
              
              <Button
                size="sm"
                variant="ghost"
                onClick={() => resolveConflict(conflict, 'skip')}
                disabled={resolving === conflict.recordId}
              >
                Skip
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
      
      <div className="flex justify-center">
        <Button onClick={loadConflicts} variant="outline">
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>
    </div>
  );
}
