# 🧹 LDIS System Maintenance Guide

## ✅ What Was Just Cleaned

Your system cleanup removed:
- **7 items total** including:
  - `.next/` directory (Next.js build cache)
  - `next.config.ts` (duplicate config file)
  - 3 temporary/backup files from node_modules
  - 2 empty directories

## 🔄 Regular Cleanup Commands

### Quick Cleanup (Run Anytime)
```bash
# Use the automated cleanup script
node cleanup-system.js
```

### Manual Cleanup Commands
```bash
# Clean Next.js build artifacts
rm -rf .next
rm -rf dist
rm -rf build

# Clean dependencies (if needed)
rm -rf node_modules
pnpm install

# Clean package manager cache
pnpm store prune
```

### Development Cleanup
```bash
# Clear Next.js cache
pnpm build --clean

# Reset development environment
rm -rf .next node_modules
pnpm install
pnpm dev
```

## 📦 Dependency Management

### Check for Outdated Packages
```bash
# Check what's outdated
pnpm outdated

# Update all dependencies
pnpm update

# Update specific package
pnpm update package-name
```

### Remove Unused Dependencies
```bash
# Analyze bundle size
pnpm build
# Check .next/analyze/ for bundle analysis

# Remove unused packages manually from package.json
# Then run: pnpm install
```

## 🗄️ Database Maintenance

### SQLite Database Cleanup
```bash
# Backup database
cp data/ldis.db data/ldis_backup_$(date +%Y%m%d).db

# Vacuum database (optimize)
sqlite3 data/ldis.db "VACUUM;"

# Check database integrity
sqlite3 data/ldis.db "PRAGMA integrity_check;"
```

### Clean Old Backups
```bash
# Remove backups older than 30 days
find data/ -name "ldis_backup_*.db" -mtime +30 -delete
```

## 🌐 Browser & Cache Cleanup

### Clear Development Cache
1. **Browser DevTools**: F12 → Application → Storage → Clear Site Data
2. **Hard Refresh**: Ctrl+Shift+R (Windows) / Cmd+Shift+R (Mac)
3. **Clear localStorage**: `localStorage.clear()` in console

### Service Worker Reset
```javascript
// In browser console
navigator.serviceWorker.getRegistrations().then(function(registrations) {
  for(let registration of registrations) {
    registration.unregister();
  }
});
```

## 📁 File System Cleanup

### Safe to Delete
- `.next/` - Next.js build cache
- `node_modules/` - Dependencies (reinstall with `pnpm install`)
- `*.log` - Log files
- `*.tmp`, `*.temp` - Temporary files
- `Thumbs.db`, `.DS_Store` - OS cache files

### Keep These Files
- `data/ldis.db` - Your database
- `src/` - Source code
- `public/` - Static assets
- `package.json` - Dependencies
- `pnpm-lock.yaml` - Lock file
- Config files (`.env`, `tsconfig.json`, etc.)

## 🔧 Performance Optimization

### Bundle Analysis
```bash
# Build with analysis
pnpm build

# Check bundle size
ls -la .next/static/chunks/
```

### Memory Usage
```bash
# Check Node.js memory usage
node --max-old-space-size=4096 src/scripts/init-database.js
```

## 📅 Maintenance Schedule

### Daily (During Development)
- Clear browser cache when testing
- Remove `.next/` if build issues occur

### Weekly
- Run `node cleanup-system.js`
- Check for outdated dependencies
- Clear old log files

### Monthly
- Update dependencies: `pnpm update`
- Vacuum SQLite database
- Remove old database backups
- Review and remove unused code

### As Needed
- Clear `node_modules/` if dependency issues
- Reset development environment
- Clean up old branches in git

## 🚨 Troubleshooting

### Build Issues
```bash
# Nuclear option - reset everything
rm -rf .next node_modules
pnpm install
pnpm dev
```

### Database Issues
```bash
# Recreate database
rm data/ldis.db
node src/scripts/init-database.js
```

### Cache Issues
```bash
# Clear all caches
rm -rf .next
pnpm store prune
# Clear browser cache manually
```

## 🎯 Quick Commands Reference

```bash
# Full system reset
rm -rf .next node_modules && pnpm install

# Quick cleanup
node cleanup-system.js

# Database reset
rm data/ldis.db && node src/scripts/init-database.js

# Update everything
pnpm update && pnpm build
```

Your LDIS system is now clean and optimized! 🎉
