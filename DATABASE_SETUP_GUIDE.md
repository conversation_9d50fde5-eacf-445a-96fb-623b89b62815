# LDIS Database Setup Guide

## 🎯 Quick Start - Recreate Database

If you ever need to recreate your `ldis.db` file, here are the methods:

### Method 1: Using the Initialization Script (Recommended)

```bash
# Run the database initialization script
node src/scripts/init-database.js
```

This script will:
- ✅ Create the `data/` directory if it doesn't exist
- ✅ Create `data/ldis.db` SQLite database file
- ✅ Create all required tables (users, templates, documents, sync_metadata)
- ✅ Create performance indexes
- ✅ Enable WAL mode for better performance

### Method 2: Using Your App's Built-in Initialization

Your LDIS app has built-in database initialization. Simply:

1. Start your Next.js app:
   ```bash
   pnpm dev
   ```

2. Navigate to any page that uses the database (like login or templates)
3. The app will automatically create the database on first use

### Method 3: Manual SQLite Creation

If you want to create it manually using SQLite CLI:

```bash
# Install sqlite3 if you don't have it
# Windows: Download from https://sqlite.org/download.html
# Mac: brew install sqlite
# Linux: sudo apt install sqlite3

# Create the database
sqlite3 data/ldis.db

# Then run the SQL commands (see below)
```

## 📋 Database Schema

Your LDIS database contains these tables:

### 1. Users Table
```sql
CREATE TABLE users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  recovery_options TEXT, -- JSON string
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 2. Templates Table
```sql
CREATE TABLE templates (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  filename TEXT NOT NULL,
  placeholders TEXT, -- JSON array
  layout_size TEXT DEFAULT 'A4',
  has_applicant_photo BOOLEAN DEFAULT FALSE,
  uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 3. Documents Table
```sql
CREATE TABLE documents (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  template_id TEXT NOT NULL,
  document_data TEXT, -- JSON string of form data
  generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (template_id) REFERENCES templates (id)
);
```

### 4. Sync Metadata Table
```sql
CREATE TABLE sync_metadata (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  table_name TEXT NOT NULL,
  record_id TEXT NOT NULL,
  operation TEXT NOT NULL, -- 'create', 'update', 'delete'
  sync_status TEXT DEFAULT 'pending', -- 'pending', 'synced', 'conflict'
  local_timestamp DATETIME NOT NULL,
  remote_timestamp DATETIME,
  conflict_data TEXT, -- JSON string for conflict resolution
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(table_name, record_id, operation)
);
```

## 🔧 Database Management Commands

### View Database Contents
```bash
# Open SQLite CLI
sqlite3 data/ldis.db

# List all tables
.tables

# View table structure
.schema users
.schema templates

# View data
SELECT * FROM users;
SELECT * FROM templates;

# Exit SQLite
.quit
```

### Backup Database
```bash
# Create a backup
cp data/ldis.db data/ldis_backup_$(date +%Y%m%d).db

# Or using SQLite
sqlite3 data/ldis.db ".backup data/ldis_backup.db"
```

### Reset Database
```bash
# Delete the database file
rm data/ldis.db

# Recreate it
node src/scripts/init-database.js
```

## 🌐 Hybrid Database System

Your LDIS project uses a hybrid database system:

- **Online**: Uses Supabase (PostgreSQL in the cloud)
- **Offline**: Uses SQLite (local file database)
- **Auto-switching**: Automatically detects connectivity and switches

The database you just created is the **SQLite (offline)** version. When you're online and have Supabase configured, the app will use Supabase instead.

## 🚨 Troubleshooting

### Database File Not Found
- Make sure you're in the project root directory
- Run the initialization script: `node src/scripts/init-database.js`

### Permission Errors
- Ensure the `data/` directory is writable
- On Linux/Mac: `chmod 755 data/`

### Corruption Issues
- Delete the database file and recreate it
- Check disk space and file system integrity

## 📁 File Locations

- **Database file**: `data/ldis.db`
- **Initialization script**: `src/scripts/init-database.js`
- **Database modules**: `src/lib/database-*.ts`
- **Models**: `src/lib/models/`

Your database is now ready! 🎉
