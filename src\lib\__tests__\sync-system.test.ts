/**
 * Comprehensive tests for the sync system
 * Tests offline/online scenarios, conflict resolution, and data integrity
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';

// Mock the database modules
jest.mock('../database-hybrid');
jest.mock('../sync-metadata');
jest.mock('../models/template-hybrid');
jest.mock('../models/user-hybrid');

describe('Sync System', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Sync Metadata', () => {
    it('should track changes when offline', async () => {
      const { trackChange } = await import('../sync-metadata');
      
      await trackChange('templates', 'test-template-1', 'create', {
        id: 'test-template-1',
        name: 'Test Template',
        description: 'A test template'
      });

      expect(trackChange).toHaveBeenCalledWith(
        'templates',
        'test-template-1',
        'create',
        expect.objectContaining({
          id: 'test-template-1',
          name: 'Test Template'
        })
      );
    });

    it('should get pending changes', async () => {
      const { getPendingChanges } = await import('../sync-metadata');
      
      const mockPendingChanges = [
        {
          id: 1,
          tableName: 'templates',
          recordId: 'test-template-1',
          operation: 'create',
          syncStatus: 'pending',
          localTimestamp: new Date().toISOString()
        }
      ];

      (getPendingChanges as jest.MockedFunction<typeof getPendingChanges>)
        .mockResolvedValue(mockPendingChanges);

      const changes = await getPendingChanges('templates');
      expect(changes).toEqual(mockPendingChanges);
    });

    it('should mark changes as synced', async () => {
      const { markAsSynced } = await import('../sync-metadata');
      
      await markAsSynced('templates', 'test-template-1', 'create');

      expect(markAsSynced).toHaveBeenCalledWith(
        'templates',
        'test-template-1',
        'create'
      );
    });

    it('should mark changes as conflict', async () => {
      const { markAsConflict } = await import('../sync-metadata');
      
      const conflictData = {
        error: 'Version conflict',
        localData: { name: 'Local Name' },
        remoteData: { name: 'Remote Name' }
      };

      await markAsConflict('templates', 'test-template-1', 'update', conflictData);

      expect(markAsConflict).toHaveBeenCalledWith(
        'templates',
        'test-template-1',
        'update',
        conflictData
      );
    });
  });

  describe('Template Sync', () => {
    it('should sync template creation', async () => {
      const { syncTemplateChanges } = await import('../models/template-hybrid');
      
      const mockResult = {
        success: true,
        synced: 1,
        errors: []
      };

      (syncTemplateChanges as jest.MockedFunction<typeof syncTemplateChanges>)
        .mockResolvedValue(mockResult);

      const result = await syncTemplateChanges();
      
      expect(result.success).toBe(true);
      expect(result.synced).toBe(1);
      expect(result.errors).toHaveLength(0);
    });

    it('should handle sync errors gracefully', async () => {
      const { syncTemplateChanges } = await import('../models/template-hybrid');
      
      const mockResult = {
        success: false,
        synced: 0,
        errors: ['Network error']
      };

      (syncTemplateChanges as jest.MockedFunction<typeof syncTemplateChanges>)
        .mockResolvedValue(mockResult);

      const result = await syncTemplateChanges();
      
      expect(result.success).toBe(false);
      expect(result.errors).toContain('Network error');
    });

    it('should handle conflicts during sync', async () => {
      const { syncTemplateChanges } = await import('../models/template-hybrid');
      
      const mockResult = {
        success: false,
        synced: 1,
        errors: [
          {
            recordId: 'test-template-1',
            operation: 'update',
            error: 'Conflict detected'
          }
        ]
      };

      (syncTemplateChanges as jest.MockedFunction<typeof syncTemplateChanges>)
        .mockResolvedValue(mockResult);

      const result = await syncTemplateChanges();
      
      expect(result.success).toBe(false);
      expect(result.synced).toBe(1);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]).toMatchObject({
        recordId: 'test-template-1',
        operation: 'update',
        error: 'Conflict detected'
      });
    });
  });

  describe('User Sync', () => {
    it('should sync user creation', async () => {
      const { syncUserChanges } = await import('../models/user-hybrid');
      
      const mockResult = {
        success: true,
        synced: 1,
        errors: []
      };

      (syncUserChanges as jest.MockedFunction<typeof syncUserChanges>)
        .mockResolvedValue(mockResult);

      const result = await syncUserChanges();
      
      expect(result.success).toBe(true);
      expect(result.synced).toBe(1);
      expect(result.errors).toHaveLength(0);
    });

    it('should handle user sync errors', async () => {
      const { syncUserChanges } = await import('../models/user-hybrid');
      
      const mockResult = {
        success: false,
        synced: 0,
        errors: ['Authentication error']
      };

      (syncUserChanges as jest.MockedFunction<typeof syncUserChanges>)
        .mockResolvedValue(mockResult);

      const result = await syncUserChanges();
      
      expect(result.success).toBe(false);
      expect(result.errors).toContain('Authentication error');
    });
  });

  describe('Database Status', () => {
    it('should detect online status', async () => {
      const { getDatabaseStatus } = await import('../database-hybrid');
      
      const mockStatus = {
        isOnline: true,
        usingSupabase: true,
        usingSQLite: false,
        supabaseConfigured: true,
        lastCheck: new Date().toISOString()
      };

      (getDatabaseStatus as jest.MockedFunction<typeof getDatabaseStatus>)
        .mockResolvedValue(mockStatus);

      const status = await getDatabaseStatus();
      
      expect(status.isOnline).toBe(true);
      expect(status.usingSupabase).toBe(true);
      expect(status.usingSQLite).toBe(false);
    });

    it('should detect offline status', async () => {
      const { getDatabaseStatus } = await import('../database-hybrid');
      
      const mockStatus = {
        isOnline: false,
        usingSupabase: false,
        usingSQLite: true,
        supabaseConfigured: true,
        lastCheck: new Date().toISOString()
      };

      (getDatabaseStatus as jest.MockedFunction<typeof getDatabaseStatus>)
        .mockResolvedValue(mockStatus);

      const status = await getDatabaseStatus();
      
      expect(status.isOnline).toBe(false);
      expect(status.usingSupabase).toBe(false);
      expect(status.usingSQLite).toBe(true);
    });
  });

  describe('Full Sync Process', () => {
    it('should perform complete sync when online', async () => {
      const { syncAllData } = await import('../sync-service');
      
      const mockResult = {
        success: true,
        totalSynced: 3,
        errors: [],
        details: {
          users: { synced: 1, errors: [] },
          templates: { synced: 1, errors: [] },
          documents: { synced: 1, errors: [] }
        }
      };

      (syncAllData as jest.MockedFunction<typeof syncAllData>)
        .mockResolvedValue(mockResult);

      const result = await syncAllData();
      
      expect(result.success).toBe(true);
      expect(result.totalSynced).toBe(3);
      expect(result.details.users.synced).toBe(1);
      expect(result.details.templates.synced).toBe(1);
      expect(result.details.documents.synced).toBe(1);
    });

    it('should handle partial sync failures', async () => {
      const { syncAllData } = await import('../sync-service');
      
      const mockResult = {
        success: false,
        totalSynced: 2,
        errors: ['Template sync failed'],
        details: {
          users: { synced: 1, errors: [] },
          templates: { synced: 0, errors: ['Template sync failed'] },
          documents: { synced: 1, errors: [] }
        }
      };

      (syncAllData as jest.MockedFunction<typeof syncAllData>)
        .mockResolvedValue(mockResult);

      const result = await syncAllData();
      
      expect(result.success).toBe(false);
      expect(result.totalSynced).toBe(2);
      expect(result.errors).toContain('Template sync failed');
      expect(result.details.templates.errors).toContain('Template sync failed');
    });

    it('should not sync when offline', async () => {
      const { syncAllData } = await import('../sync-service');
      
      const mockResult = {
        success: false,
        totalSynced: 0,
        errors: ['Not online or Supabase not configured'],
        details: {
          users: { synced: 0, errors: [] },
          templates: { synced: 0, errors: [] },
          documents: { synced: 0, errors: [] }
        }
      };

      (syncAllData as jest.MockedFunction<typeof syncAllData>)
        .mockResolvedValue(mockResult);

      const result = await syncAllData();
      
      expect(result.success).toBe(false);
      expect(result.totalSynced).toBe(0);
      expect(result.errors).toContain('Not online or Supabase not configured');
    });
  });

  describe('Sync Statistics', () => {
    it('should get accurate sync statistics', async () => {
      const { getSyncStats } = await import('../sync-metadata');
      
      const mockStats = {
        pending: 5,
        synced: 10,
        conflicts: 2,
        total: 17
      };

      (getSyncStats as jest.MockedFunction<typeof getSyncStats>)
        .mockResolvedValue(mockStats);

      const stats = await getSyncStats();
      
      expect(stats.pending).toBe(5);
      expect(stats.synced).toBe(10);
      expect(stats.conflicts).toBe(2);
      expect(stats.total).toBe(17);
    });
  });
});
