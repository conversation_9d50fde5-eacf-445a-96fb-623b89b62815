// Hybrid template model - automatically switches between Supabase and SQLite based on connectivity

import { getDatabaseStatus } from '../database-hybrid';
import { Template } from '@/types/template';

// Dynamic imports for model implementations
let supabaseTemplateModel: any;
let sqliteTemplateModel: any;

// Initialize model modules
const initModelModules = () => {
  if (!supabaseTemplateModel) {
    supabaseTemplateModel = require('./template-supabase');
  }
  if (!sqliteTemplateModel) {
    sqliteTemplateModel = require('./template-sqlite-backup');
  }
};

// Get the appropriate model implementation
const getTemplateModelImpl = async () => {
  initModelModules();
  
  const status = await getDatabaseStatus();
  return status.usingSupabase ? supabaseTemplateModel : sqliteTemplateModel;
};

// Hybrid TemplateModel class
export class TemplateModel {
  static async findById(id: string): Promise<Template | null> {
    const impl = await getTemplateModelImpl();
    return impl.TemplateModel.findById(id);
  }

  static async findAll(): Promise<Template[]> {
    const impl = await getTemplateModelImpl();
    return impl.TemplateModel.findAll();
  }

  static async create(template: Omit<Template, 'uploadedAt'>): Promise<Template> {
    const impl = await getTemplateModelImpl();
    return impl.TemplateModel.create(template);
  }

  static async update(id: string, updates: Partial<Omit<Template, 'id' | 'uploadedAt'>>): Promise<Template | null> {
    const impl = await getTemplateModelImpl();
    return impl.TemplateModel.update(id, updates);
  }

  static async delete(id: string): Promise<boolean> {
    const impl = await getTemplateModelImpl();
    return impl.TemplateModel.delete(id);
  }

  static async search(query: string): Promise<Template[]> {
    const impl = await getTemplateModelImpl();
    return impl.TemplateModel.search(query);
  }

  static async getTemplateStats(): Promise<{
    totalTemplates: number;
    templatesThisWeek: number;
    templatesThisMonth: number;
  }> {
    const impl = await getTemplateModelImpl();
    return impl.TemplateModel.getTemplateStats();
  }
}

// Document Record interface
export interface DocumentRecord {
  id?: number;
  templateId: string;
  documentData: Record<string, any>;
  generatedAt?: string;
}

// Hybrid DocumentModel class
export class DocumentModel {
  static async create(document: Omit<DocumentRecord, 'id' | 'generatedAt'>): Promise<DocumentRecord> {
    const impl = await getTemplateModelImpl();
    return impl.DocumentModel.create(document);
  }

  static async findByTemplateId(templateId: string, limit?: number): Promise<DocumentRecord[]> {
    const impl = await getTemplateModelImpl();
    return impl.DocumentModel.findByTemplateId(templateId, limit);
  }

  static async findAll(limit?: number): Promise<DocumentRecord[]> {
    const impl = await getTemplateModelImpl();
    return impl.DocumentModel.findAll(limit);
  }

  static async delete(id: number): Promise<boolean> {
    const impl = await getTemplateModelImpl();
    return impl.DocumentModel.delete(id);
  }

  static async getDocumentStats(): Promise<{
    totalDocuments: number;
    documentsToday: number;
    documentsThisWeek: number;
    documentsThisMonth: number;
  }> {
    const impl = await getTemplateModelImpl();
    return impl.DocumentModel.getDocumentStats();
  }
}

// Sync functions for offline changes
export const syncTemplateChanges = async (): Promise<{
  success: boolean;
  synced: number;
  errors: any[];
}> => {
  console.log('🔄 Starting template sync...');

  try {
    const { getPendingChanges, markAsSynced, markAsConflict } = await import('../sync-metadata');

    // Get pending template changes
    const pendingChanges = await getPendingChanges('templates');

    if (pendingChanges.length === 0) {
      console.log('✅ No pending template changes to sync');
      return { success: true, synced: 0, errors: [] };
    }

    console.log(`📋 Found ${pendingChanges.length} pending template changes`);

    // Get both model implementations
    initModelModules();
    const supabaseModel = supabaseTemplateModel.TemplateModel;
    const sqliteModel = sqliteTemplateModel.TemplateModel;

    let syncedCount = 0;
    const errors: any[] = [];

    for (const change of pendingChanges) {
      try {
        console.log(`🔄 Syncing ${change.operation} for template ${change.recordId}`);

        if (change.operation === 'create') {
          // Create template in Supabase
          const templateData = change.conflictData;
          await supabaseModel.create(templateData);
          await markAsSynced('templates', change.recordId, 'create');
          syncedCount++;

        } else if (change.operation === 'update') {
          // Check if template exists in Supabase
          const existingTemplate = await supabaseModel.findById(change.recordId);

          if (existingTemplate) {
            // Update template in Supabase
            const updates = change.conflictData;
            await supabaseModel.update(change.recordId, updates);
            await markAsSynced('templates', change.recordId, 'update');
            syncedCount++;
          } else {
            // Template doesn't exist in Supabase, get full data from SQLite and create
            const fullTemplate = await sqliteModel.findById(change.recordId);
            if (fullTemplate) {
              await supabaseModel.create(fullTemplate);
              await markAsSynced('templates', change.recordId, 'update');
              syncedCount++;
            } else {
              throw new Error(`Template ${change.recordId} not found in local database`);
            }
          }

        } else if (change.operation === 'delete') {
          // Delete template from Supabase
          await supabaseModel.delete(change.recordId);
          await markAsSynced('templates', change.recordId, 'delete');
          syncedCount++;
        }

      } catch (error) {
        console.error(`❌ Failed to sync ${change.operation} for template ${change.recordId}:`, error);

        // Mark as conflict for manual resolution
        await markAsConflict('templates', change.recordId, change.operation, {
          error: error instanceof Error ? error.message : 'Unknown error',
          originalData: change.conflictData,
          timestamp: new Date().toISOString()
        });

        errors.push({
          recordId: change.recordId,
          operation: change.operation,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    console.log(`✅ Template sync completed: ${syncedCount} synced, ${errors.length} errors`);

    return {
      success: errors.length === 0,
      synced: syncedCount,
      errors
    };

  } catch (error) {
    console.error('❌ Template sync failed:', error);
    return {
      success: false,
      synced: 0,
      errors: [error instanceof Error ? error.message : 'Unknown error']
    };
  }
};

export const syncDocumentChanges = async (): Promise<{
  success: boolean;
  synced: number;
  errors: any[];
}> => {
  console.log('🔄 Starting document sync...');

  try {
    const { getPendingChanges, markAsSynced, markAsConflict } = await import('../sync-metadata');

    // Get pending document changes
    const pendingChanges = await getPendingChanges('documents');

    if (pendingChanges.length === 0) {
      console.log('✅ No pending document changes to sync');
      return { success: true, synced: 0, errors: [] };
    }

    console.log(`📋 Found ${pendingChanges.length} pending document changes`);

    // Get both model implementations
    initModelModules();
    const supabaseModel = supabaseTemplateModel.DocumentModel;
    const sqliteModel = sqliteTemplateModel.DocumentModel;

    let syncedCount = 0;
    const errors: any[] = [];

    for (const change of pendingChanges) {
      try {
        console.log(`🔄 Syncing ${change.operation} for document ${change.recordId}`);

        if (change.operation === 'create') {
          // Create document in Supabase
          const documentData = change.conflictData;
          // Remove the local ID since Supabase will generate its own
          const { id, ...dataWithoutId } = documentData;
          await supabaseModel.create(dataWithoutId);
          await markAsSynced('documents', change.recordId, 'create');
          syncedCount++;
        }
        // Note: Documents are typically only created, not updated or deleted
        // But we can add those operations if needed in the future

      } catch (error) {
        console.error(`❌ Failed to sync ${change.operation} for document ${change.recordId}:`, error);

        // Mark as conflict for manual resolution
        await markAsConflict('documents', change.recordId, change.operation, {
          error: error instanceof Error ? error.message : 'Unknown error',
          originalData: change.conflictData,
          timestamp: new Date().toISOString()
        });

        errors.push({
          recordId: change.recordId,
          operation: change.operation,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    console.log(`✅ Document sync completed: ${syncedCount} synced, ${errors.length} errors`);

    return {
      success: errors.length === 0,
      synced: syncedCount,
      errors
    };

  } catch (error) {
    console.error('❌ Document sync failed:', error);
    return {
      success: false,
      synced: 0,
      errors: [error instanceof Error ? error.message : 'Unknown error']
    };
  }
};
