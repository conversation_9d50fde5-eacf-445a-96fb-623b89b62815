#!/usr/bin/env tsx

/**
 * Integration test script for the sync system
 * Run with: npx tsx src/scripts/test-sync.ts
 */

import { getDatabaseStatus } from '../lib/database-hybrid';
import { trackChange, getPendingChanges, getSyncStats, cleanupSyncedRecords } from '../lib/sync-metadata';
import { syncAllData, getSyncStatus } from '../lib/sync-service';

async function testSyncSystem() {
  console.log('🧪 Testing Sync System Integration\n');

  try {
    // Test 1: Database Status
    console.log('1️⃣ Testing Database Status...');
    const dbStatus = await getDatabaseStatus();
    console.log('   Database Status:', {
      isOnline: dbStatus.isOnline,
      usingSupabase: dbStatus.usingSupabase,
      usingSQLite: dbStatus.usingSQLite,
      supabaseConfigured: dbStatus.supabaseConfigured
    });
    console.log('   ✅ Database status check passed\n');

    // Test 2: Sync Metadata
    console.log('2️⃣ Testing Sync Metadata...');
    
    // Track a test change
    await trackChange('templates', 'test-template-sync', 'create', {
      id: 'test-template-sync',
      name: 'Test Sync Template',
      description: 'Testing sync functionality'
    });
    console.log('   ✅ Change tracked successfully');

    // Get pending changes
    const pendingChanges = await getPendingChanges('templates');
    console.log(`   📋 Found ${pendingChanges.length} pending template changes`);

    // Get sync stats
    const syncStats = await getSyncStats();
    console.log('   📊 Sync Stats:', syncStats);
    console.log('   ✅ Sync metadata tests passed\n');

    // Test 3: Sync Status
    console.log('3️⃣ Testing Sync Status...');
    const syncStatus = await getSyncStatus();
    console.log('   Sync Status:', {
      isOnline: syncStatus.isOnline,
      pendingChanges: syncStatus.pendingChanges,
      autoSyncEnabled: syncStatus.autoSyncEnabled,
      lastSync: syncStatus.lastSync
    });
    console.log('   ✅ Sync status check passed\n');

    // Test 4: Full Sync (only if online)
    if (dbStatus.isOnline && dbStatus.usingSupabase) {
      console.log('4️⃣ Testing Full Sync...');
      const syncResult = await syncAllData();
      console.log('   Sync Result:', {
        success: syncResult.success,
        totalSynced: syncResult.totalSynced,
        errorCount: syncResult.errors.length
      });
      console.log('   Details:', syncResult.details);
      console.log('   ✅ Full sync test completed\n');
    } else {
      console.log('4️⃣ Skipping Full Sync (offline or Supabase not configured)\n');
    }

    // Test 5: Cleanup
    console.log('5️⃣ Testing Cleanup...');
    const cleanedCount = await cleanupSyncedRecords(0); // Clean all synced records
    console.log(`   🧹 Cleaned up ${cleanedCount} old sync records`);
    console.log('   ✅ Cleanup test passed\n');

    console.log('🎉 All sync system tests completed successfully!');

  } catch (error) {
    console.error('❌ Sync system test failed:', error);
    process.exit(1);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testSyncSystem().catch(console.error);
}

export { testSyncSystem };
