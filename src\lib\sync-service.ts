// Sync service for offline/online data synchronization

import { getDatabaseStatus, syncOfflineToOnline } from './database-hybrid';
import { syncUserChanges } from './models/user-hybrid';
import { syncTemplateChanges, syncDocumentChanges } from './models/template-hybrid';

export interface SyncResult {
  success: boolean;
  totalSynced: number;
  errors: any[];
  details: {
    users: { synced: number; errors: any[] };
    templates: { synced: number; errors: any[] };
    documents: { synced: number; errors: any[] };
  };
}

export interface SyncStatus {
  isOnline: boolean;
  lastSync: string | null;
  pendingChanges: number;
  autoSyncEnabled: boolean;
}

// Sync status storage (in a real app, this might be in localStorage or a database)
let syncStatus: SyncStatus = {
  isOnline: false,
  lastSync: null,
  pendingChanges: 0,
  autoSyncEnabled: true
};

// Main sync function
export const syncAllData = async (): Promise<SyncResult> => {
  console.log('🔄 Starting data synchronization...');
  
  const status = await getDatabaseStatus();
  
  if (!status.isOnline || !status.supabaseConfigured) {
    return {
      success: false,
      totalSynced: 0,
      errors: ['Not online or Supabase not configured'],
      details: {
        users: { synced: 0, errors: [] },
        templates: { synced: 0, errors: [] },
        documents: { synced: 0, errors: [] }
      }
    };
  }

  try {
    // Sync different data types
    const [userSync, templateSync, documentSync] = await Promise.all([
      syncUserChanges(),
      syncTemplateChanges(),
      syncDocumentChanges()
    ]);

    const totalSynced = userSync.synced + templateSync.synced + documentSync.synced;
    const allErrors = [...userSync.errors, ...templateSync.errors, ...documentSync.errors];

    // Update sync status
    syncStatus.lastSync = new Date().toISOString();
    syncStatus.isOnline = true;
    syncStatus.pendingChanges = Math.max(0, syncStatus.pendingChanges - totalSynced);

    console.log(`✅ Sync completed: ${totalSynced} items synced`);

    return {
      success: allErrors.length === 0,
      totalSynced,
      errors: allErrors,
      details: {
        users: { synced: userSync.synced, errors: userSync.errors },
        templates: { synced: templateSync.synced, errors: templateSync.errors },
        documents: { synced: documentSync.synced, errors: documentSync.errors }
      }
    };

  } catch (error) {
    console.error('❌ Sync failed:', error);
    
    return {
      success: false,
      totalSynced: 0,
      errors: [error],
      details: {
        users: { synced: 0, errors: [error] },
        templates: { synced: 0, errors: [error] },
        documents: { synced: 0, errors: [error] }
      }
    };
  }
};

// Get current sync status
export const getSyncStatus = async (): Promise<SyncStatus> => {
  const dbStatus = await getDatabaseStatus();

  // Update pending changes count from actual data
  await updatePendingChangesCount();

  return {
    ...syncStatus,
    isOnline: dbStatus.isOnline
  };
};

// Enable/disable auto-sync
export const setAutoSync = (enabled: boolean) => {
  syncStatus.autoSyncEnabled = enabled;
  
  if (enabled) {
    startAutoSync();
  } else {
    stopAutoSync();
  }
};

// Auto-sync functionality
let autoSyncInterval: NodeJS.Timeout | null = null;
const AUTO_SYNC_INTERVAL = 5 * 60 * 1000; // 5 minutes

export const startAutoSync = () => {
  if (autoSyncInterval) {
    clearInterval(autoSyncInterval);
  }
  
  autoSyncInterval = setInterval(async () => {
    if (syncStatus.autoSyncEnabled) {
      const status = await getDatabaseStatus();
      
      if (status.isOnline && syncStatus.pendingChanges > 0) {
        console.log('🔄 Auto-sync triggered');
        await syncAllData();
      }
    }
  }, AUTO_SYNC_INTERVAL);
  
  console.log('🔄 Auto-sync started');
};

export const stopAutoSync = () => {
  if (autoSyncInterval) {
    clearInterval(autoSyncInterval);
    autoSyncInterval = null;
  }
  
  console.log('⏹️ Auto-sync stopped');
};

// Track pending changes (call this when data is modified offline)
export const trackPendingChange = () => {
  syncStatus.pendingChanges++;
};

// Update pending changes count from actual sync metadata
export const updatePendingChangesCount = async (): Promise<void> => {
  try {
    const { getSyncStats } = await import('./sync-metadata');
    const stats = await getSyncStats();
    syncStatus.pendingChanges = stats.pending;
  } catch (error) {
    console.error('Error updating pending changes count:', error);
  }
};

// Force sync when coming back online
export const handleOnlineEvent = async () => {
  console.log('🌐 Device came online, checking for pending changes...');
  
  if (syncStatus.pendingChanges > 0) {
    await syncAllData();
  }
};

// Browser-side event listeners
if (typeof window !== 'undefined') {
  window.addEventListener('online', handleOnlineEvent);
  
  window.addEventListener('offline', () => {
    console.log('📱 Device went offline');
    syncStatus.isOnline = false;
  });
  
  // Start auto-sync by default
  if (syncStatus.autoSyncEnabled) {
    startAutoSync();
  }
}

// Cleanup function
export const cleanup = () => {
  stopAutoSync();
  
  if (typeof window !== 'undefined') {
    window.removeEventListener('online', handleOnlineEvent);
  }
};
